using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public enum ConfigGroupId
    {
        General = 100,
        BackgroundProcessors = 200,
        UI = 300
    }
    public class Config
    {
        [Key]
        public int ConfigId { get; set; }

        [Required]
        public int GroupId { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Key { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string Value { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string? Description { get; set; }
        
        [Required]
        [StringLength(20)]
        public string DataType { get; set; } = "string"; // string, int, bool, decimal
        
        public bool IsEnabled { get; set; } = true;

        public bool Visible { get; set; } = true;

        public DateTime CreatedUTC { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedUTC { get; set; }
    }
    
    public class ConfigGroup
    {
        public int GroupId { get; set; }
        public string DisplayName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public List<Config> Configs { get; set; } = new List<Config>();
    }
    
    public class ConfigUpdateRequest
    {
        [Required]
        public int ConfigId { get; set; }
        
        [Required]
        [StringLength(500)]
        public string Value { get; set; } = string.Empty;
    }
}
