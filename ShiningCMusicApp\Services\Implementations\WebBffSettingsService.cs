using Microsoft.AspNetCore.Components.WebAssembly.Http;
using Microsoft.Extensions.Logging;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class WebBffSettingsService : IWebBffSettingsService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<WebBffSettingsService> _logger;
        private readonly string _baseUrl;

        public WebBffSettingsService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<WebBffSettingsService> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<SettingsData?> GetSettingsDataAsync()
        {
            try
            {
                _logger.LogInformation("Fetching settings data from Web BFF");

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/settings/data");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var data = JsonSerializer.Deserialize<SettingsData>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Settings data loaded successfully. Config groups: {Count}", data?.ConfigGroups?.Count ?? 0);
                    return data;
                }

                _logger.LogWarning("Failed to fetch settings data. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching settings data from Web BFF");
                return null;
            }
        }

        public async Task<Config?> GetConfigAsync(int configId)
        {
            try
            {
                _logger.LogInformation("Fetching config {ConfigId} from Web BFF", configId);

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/settings/config/{configId}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var config = JsonSerializer.Deserialize<Config>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Config {ConfigId} loaded successfully", configId);
                    return config;
                }

                _logger.LogWarning("Failed to fetch config {ConfigId}. Status: {StatusCode}", configId, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching config {ConfigId} from Web BFF", configId);
                return null;
            }
        }

        public async Task<bool> UpdateConfigAsync(int configId, string value)
        {
            try
            {
                _logger.LogInformation("Updating config {ConfigId} via Web BFF", configId);

                var requestData = new { Value = value };
                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/settings/config/{configId}")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Config {ConfigId} updated successfully", configId);
                    return true;
                }

                _logger.LogWarning("Failed to update config {ConfigId}. Status: {StatusCode}", configId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating config {ConfigId} via Web BFF", configId);
                return false;
            }
        }

        public async Task<BatchUpdateResult> BatchUpdateConfigsAsync(List<ConfigUpdate> updates)
        {
            try
            {
                _logger.LogInformation("Batch updating {Count} configs via Web BFF", updates.Count);

                var requestData = new { Updates = updates };
                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/settings/config/batch-update")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<BatchUpdateResult>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Batch config update completed. Success: {Success}", result?.Success ?? false);
                    return result ?? new BatchUpdateResult { Success = false, Message = "Unknown error" };
                }

                _logger.LogWarning("Failed to batch update configs. Status: {StatusCode}", response.StatusCode);
                return new BatchUpdateResult { Success = false, Message = $"HTTP {response.StatusCode}" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error batch updating configs via Web BFF");
                return new BatchUpdateResult { Success = false, Message = ex.Message };
            }
        }

        public async Task<Dictionary<string, bool>> GetBackgroundProcessorStatesAsync()
        {
            try
            {
                _logger.LogInformation("Fetching background processor states from Web BFF");

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/settings/background-processors");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var states = JsonSerializer.Deserialize<Dictionary<string, bool>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Background processor states loaded successfully. Count: {Count}", states?.Count ?? 0);
                    return states ?? new Dictionary<string, bool>();
                }

                _logger.LogWarning("Failed to fetch background processor states. Status: {StatusCode}", response.StatusCode);
                return new Dictionary<string, bool>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching background processor states from Web BFF");
                return new Dictionary<string, bool>();
            }
        }

        public async Task<bool> ToggleBackgroundProcessorAsync(string processorName, bool enabled)
        {
            try
            {
                _logger.LogInformation("Toggling background processor {ProcessorName} to {Enabled} via Web BFF", processorName, enabled);

                var requestData = new { Enabled = enabled };
                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/settings/background-processors/{processorName}/toggle")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Background processor {ProcessorName} toggled successfully", processorName);
                    return true;
                }

                _logger.LogWarning("Failed to toggle background processor {ProcessorName}. Status: {StatusCode}", processorName, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling background processor {ProcessorName} via Web BFF", processorName);
                return false;
            }
        }

        public async Task<string?> GetConfigValueAsync(int groupId, string key)
        {
            try
            {
                _logger.LogInformation("Fetching config value for group {GroupId}, key {Key} from Web BFF", groupId, key);

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/settings/config-value/{groupId}/{key}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("Config value for group {GroupId}, key {Key} loaded successfully", groupId, key);
                    return content?.Trim('"'); // Remove JSON quotes if present
                }

                _logger.LogWarning("Failed to fetch config value for group {GroupId}, key {Key}. Status: {StatusCode}", groupId, key, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching config value for group {GroupId}, key {Key} from Web BFF", groupId, key);
                return null;
            }
        }

        public async Task<T?> GetConfigValueAsync<T>(int groupId, string key, T? defaultValue = default)
        {
            try
            {
                var stringValue = await GetConfigValueAsync(groupId, key);

                if (string.IsNullOrEmpty(stringValue))
                {
                    _logger.LogInformation("Config value for group {GroupId}, key {Key} is null or empty, returning default value", groupId, key);
                    return defaultValue;
                }

                // Handle different types
                if (typeof(T) == typeof(string))
                {
                    return (T)(object)stringValue;
                }
                else if (typeof(T) == typeof(int) || typeof(T) == typeof(int?))
                {
                    if (int.TryParse(stringValue, out var intValue))
                    {
                        return (T)(object)intValue;
                    }
                }
                else if (typeof(T) == typeof(bool) || typeof(T) == typeof(bool?))
                {
                    if (bool.TryParse(stringValue, out var boolValue))
                    {
                        return (T)(object)boolValue;
                    }
                }
                else if (typeof(T) == typeof(double) || typeof(T) == typeof(double?))
                {
                    if (double.TryParse(stringValue, out var doubleValue))
                    {
                        return (T)(object)doubleValue;
                    }
                }

                _logger.LogWarning("Could not convert config value '{Value}' to type {Type} for group {GroupId}, key {Key}, returning default value", stringValue, typeof(T).Name, groupId, key);
                return defaultValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting config value for group {GroupId}, key {Key} to type {Type}", groupId, key, typeof(T).Name);
                return defaultValue;
            }
        }
    }
}
