using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;
using System.Text.Json;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/admin")]
    [Authorize(Roles = "Administrator")]
    public class AdminController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<AdminController> _logger;

        public AdminController(ApiClientService apiClient, ILogger<AdminController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        [HttpGet("data")]
        public async Task<IActionResult> GetAdminData()
        {
            try
            {
                _logger.LogInformation("Loading admin data from API");

                // Load all admin data in parallel from original API endpoints
                var usersTask = _apiClient.GetJsonAsync<List<User>>("user");
                var userRolesTask = _apiClient.GetJsonAsync<List<UserRole>>("user/roles");
                var subjectsTask = _apiClient.GetJsonAsync<List<Subject>>("subjects");
                var locationsTask = _apiClient.GetJsonAsync<List<Location>>("locations");
                var tutorsTask = _apiClient.GetJsonAsync<List<Tutor>>("tutors");
                var studentsTask = _apiClient.GetJsonAsync<List<Student>>("students");

                await Task.WhenAll(usersTask, userRolesTask, subjectsTask, locationsTask, tutorsTask, studentsTask);

                var adminData = new AdminData
                {
                    Users = (await usersTask) ?? new List<User>(),
                    UserRoles = (await userRolesTask) ?? new List<UserRole>(),
                    Subjects = (await subjectsTask) ?? new List<Subject>(),
                    Locations = (await locationsTask) ?? new List<Location>(),
                    Tutors = (await tutorsTask) ?? new List<Tutor>(),
                    Students = (await studentsTask) ?? new List<Student>()
                };

                return Ok(adminData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading admin data from API");
                return StatusCode(500, new { message = "An error occurred loading admin data" });
            }
        }

        // User Management
        [HttpPost("users")]
        public async Task<IActionResult> CreateUser([FromBody] User user)
        {
            try
            {
                _logger.LogInformation("Creating user via API: {LoginName}", user.LoginName);
                
                var response = await _apiClient.PostAsync("users", user);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdUser = JsonSerializer.Deserialize<User>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return Ok(new { success = true, message = "User created successfully", user = createdUser });
                }
                
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create user. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to create user" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user via API");
                return StatusCode(500, new { message = "An error occurred creating user" });
            }
        }

        [HttpPut("users/{loginName}")]
        public async Task<IActionResult> UpdateUser(string loginName, [FromBody] User user)
        {
            try
            {
                _logger.LogInformation("Updating user via API: {LoginName}", loginName);
                
                user.LoginName = loginName;
                var response = await _apiClient.PutAsync($"users/{loginName}", user);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "User updated successfully" });
                }
                
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update user. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to update user" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {LoginName} via API", loginName);
                return StatusCode(500, new { message = "An error occurred updating user" });
            }
        }

        [HttpDelete("users/{loginName}")]
        public async Task<IActionResult> DeleteUser(string loginName)
        {
            try
            {
                _logger.LogInformation("Deleting user via API: {LoginName}", loginName);
                
                var response = await _apiClient.DeleteAsync($"users/{loginName}");
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "User deleted successfully" });
                }
                
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete user. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to delete user" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {LoginName} via API", loginName);
                return StatusCode(500, new { message = "An error occurred deleting user" });
            }
        }

        // Subject Management
        [HttpPost("subjects")]
        public async Task<IActionResult> CreateSubject([FromBody] Subject subject)
        {
            try
            {
                _logger.LogInformation("Creating subject via API: {SubjectName}", subject.SubjectName);
                
                var response = await _apiClient.PostAsync("subjects", subject);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdSubject = JsonSerializer.Deserialize<Subject>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return Ok(new { success = true, message = "Subject created successfully", subject = createdSubject });
                }
                
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create subject. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to create subject" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subject via API");
                return StatusCode(500, new { message = "An error occurred creating subject" });
            }
        }

        [HttpPut("subjects/{subjectId}")]
        public async Task<IActionResult> UpdateSubject(int subjectId, [FromBody] Subject subject)
        {
            try
            {
                _logger.LogInformation("Updating subject via API: {SubjectId}", subjectId);
                
                subject.SubjectId = subjectId;
                var response = await _apiClient.PutAsync($"subjects/{subjectId}", subject);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Subject updated successfully" });
                }
                
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update subject. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to update subject" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating subject {SubjectId} via API", subjectId);
                return StatusCode(500, new { message = "An error occurred updating subject" });
            }
        }

        [HttpDelete("subjects/{subjectId}")]
        public async Task<IActionResult> DeleteSubject(int subjectId)
        {
            try
            {
                _logger.LogInformation("Deleting subject via API: {SubjectId}", subjectId);
                
                var response = await _apiClient.DeleteAsync($"subjects/{subjectId}");
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Subject deleted successfully" });
                }
                
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete subject. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to delete subject" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting subject {SubjectId} via API", subjectId);
                return StatusCode(500, new { message = "An error occurred deleting subject" });
            }
        }

        // Location Management
        [HttpPost("locations")]
        public async Task<IActionResult> CreateLocation([FromBody] Location location)
        {
            try
            {
                _logger.LogInformation("Creating location via API: {LocationName}", location.LocationName);

                var response = await _apiClient.PostAsync("locations", location);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdLocation = JsonSerializer.Deserialize<Location>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return Ok(new { success = true, message = "Location created successfully", location = createdLocation });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create location. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to create location" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating location via API");
                return StatusCode(500, new { message = "An error occurred creating location" });
            }
        }

        [HttpPut("locations/{locationId}")]
        public async Task<IActionResult> UpdateLocation(int locationId, [FromBody] Location location)
        {
            try
            {
                _logger.LogInformation("Updating location via API: {LocationId}", locationId);

                location.LocationId = locationId;
                var response = await _apiClient.PutAsync($"locations/{locationId}", location);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Location updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update location. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to update location" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating location {LocationId} via API", locationId);
                return StatusCode(500, new { message = "An error occurred updating location" });
            }
        }

        [HttpDelete("locations/{locationId}")]
        public async Task<IActionResult> DeleteLocation(int locationId)
        {
            try
            {
                _logger.LogInformation("Deleting location via API: {LocationId}", locationId);

                var response = await _apiClient.DeleteAsync($"locations/{locationId}");
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Location deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete location. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to delete location" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting location {LocationId} via API", locationId);
                return StatusCode(500, new { message = "An error occurred deleting location" });
            }
        }

        // Tutor Management
        [HttpPost("tutors")]
        public async Task<IActionResult> CreateTutor([FromBody] Tutor tutor)
        {
            try
            {
                _logger.LogInformation("Creating tutor via API: {TutorName}", tutor.TutorName);

                var response = await _apiClient.PostAsync("tutors", tutor);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdTutor = JsonSerializer.Deserialize<Tutor>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return Ok(new { success = true, message = "Tutor created successfully", tutor = createdTutor });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create tutor. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to create tutor" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating tutor via API");
                return StatusCode(500, new { message = "An error occurred creating tutor" });
            }
        }

        [HttpPut("tutors/{tutorId}")]
        public async Task<IActionResult> UpdateTutor(int tutorId, [FromBody] Tutor tutor)
        {
            try
            {
                _logger.LogInformation("Updating tutor via API: {TutorId}", tutorId);

                tutor.TutorId = tutorId;
                var response = await _apiClient.PutAsync($"tutors/{tutorId}", tutor);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Tutor updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update tutor. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to update tutor" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tutor {TutorId} via API", tutorId);
                return StatusCode(500, new { message = "An error occurred updating tutor" });
            }
        }

        [HttpDelete("tutors/{tutorId}")]
        public async Task<IActionResult> DeleteTutor(int tutorId)
        {
            try
            {
                _logger.LogInformation("Deleting tutor via API: {TutorId}", tutorId);

                var response = await _apiClient.DeleteAsync($"tutors/{tutorId}");
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Tutor deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete tutor. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to delete tutor" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting tutor {TutorId} via API", tutorId);
                return StatusCode(500, new { message = "An error occurred deleting tutor" });
            }
        }

        [HttpPatch("tutors/{tutorId}/color")]
        public async Task<IActionResult> UpdateTutorColor(int tutorId, [FromBody] object colorRequest)
        {
            try
            {
                _logger.LogInformation("Updating tutor {TutorId} color via API", tutorId);

                var response = await _apiClient.PatchAsync($"tutors/{tutorId}/color", colorRequest);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Tutor color updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update tutor color. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to update tutor color" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tutor {TutorId} color via API", tutorId);
                return StatusCode(500, new { message = "An error occurred updating tutor color" });
            }
        }

        // Student Management
        [HttpPost("students")]
        public async Task<IActionResult> CreateStudent([FromBody] Student student)
        {
            try
            {
                _logger.LogInformation("Creating student via API: {StudentName}", student.StudentName);

                var response = await _apiClient.PostAsync("students", student);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdStudent = JsonSerializer.Deserialize<Student>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return Ok(new { success = true, message = "Student created successfully", student = createdStudent });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create student. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to create student" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating student via API");
                return StatusCode(500, new { message = "An error occurred creating student" });
            }
        }

        [HttpPut("students/{studentId}")]
        public async Task<IActionResult> UpdateStudent(int studentId, [FromBody] Student student)
        {
            try
            {
                _logger.LogInformation("Updating student via API: {StudentId}", studentId);

                student.StudentId = studentId;
                var response = await _apiClient.PutAsync($"students/{studentId}", student);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Student updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update student. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to update student" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating student {StudentId} via API", studentId);
                return StatusCode(500, new { message = "An error occurred updating student" });
            }
        }

        [HttpDelete("students/{studentId}")]
        public async Task<IActionResult> DeleteStudent(int studentId)
        {
            try
            {
                _logger.LogInformation("Deleting student via API: {StudentId}", studentId);

                var response = await _apiClient.DeleteAsync($"students/{studentId}");
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Student deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete student. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to delete student" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting student {StudentId} via API", studentId);
                return StatusCode(500, new { message = "An error occurred deleting student" });
            }
        }

        // User Role Management
        [HttpGet("user-roles")]
        public async Task<IActionResult> GetUserRoles()
        {
            try
            {
                _logger.LogInformation("Getting user roles via API");

                var response = await _apiClient.GetAsync("user/roles");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var userRoles = JsonSerializer.Deserialize<List<UserRole>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return Ok(new { success = true, message = "User roles retrieved successfully", userRoles });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to get user roles. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to retrieve user roles" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user roles via API");
                return StatusCode(500, new { message = "An error occurred retrieving user roles" });
            }
        }

        [HttpPost("user-roles")]
        public async Task<IActionResult> CreateUserRole([FromBody] UserRole userRole)
        {
            try
            {
                _logger.LogInformation("Creating user role via API: {Description}", userRole.Description);

                var response = await _apiClient.PostAsync("users/roles", userRole);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdRole = JsonSerializer.Deserialize<UserRole>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return Ok(new { success = true, message = "User role created successfully", userRole = createdRole });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create user role. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to create user role" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user role via API");
                return StatusCode(500, new { message = "An error occurred creating user role" });
            }
        }

        [HttpPut("user-roles/{roleId}")]
        public async Task<IActionResult> UpdateUserRole(int roleId, [FromBody] UserRole userRole)
        {
            try
            {
                _logger.LogInformation("Updating user role via API: {RoleId}", roleId);

                userRole.ID = roleId;
                var response = await _apiClient.PutAsync($"users/roles/{roleId}", userRole);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "User role updated successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update user role. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to update user role" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user role {RoleId} via API", roleId);
                return StatusCode(500, new { message = "An error occurred updating user role" });
            }
        }

        [HttpDelete("user-roles/{roleId}")]
        public async Task<IActionResult> DeleteUserRole(int roleId)
        {
            try
            {
                _logger.LogInformation("Deleting user role via API: {RoleId}", roleId);

                var response = await _apiClient.DeleteAsync($"users/roles/{roleId}");
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "User role deleted successfully" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete user role. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to delete user role" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user role {RoleId} via API", roleId);
                return StatusCode(500, new { message = "An error occurred deleting user role" });
            }
        }
    }

    public class AdminData
    {
        public List<User> Users { get; set; } = new();
        public List<UserRole> UserRoles { get; set; } = new();
        public List<Subject> Subjects { get; set; } = new();
        public List<Location> Locations { get; set; } = new();
        public List<Tutor> Tutors { get; set; } = new();
        public List<Student> Students { get; set; } = new();
    }
}
