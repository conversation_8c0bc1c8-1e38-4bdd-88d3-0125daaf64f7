using Microsoft.JSInterop;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Enums;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Implementations
{
    public class SidebarThemeService : ISidebarThemeService
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly ILogger<SidebarThemeService> _logger;
        private readonly AppConfiguration _appConfig;
        private readonly IConfigApiService _configApiService;
        private SidebarTheme _currentTheme = SidebarTheme.BluePurple;

        public SidebarThemeService(IJSRuntime jsRuntime, ILogger<SidebarThemeService> logger, AppConfiguration appConfig, IConfigApiService configApiService)
        {
            _jsRuntime = jsRuntime;
            _logger = logger;
            _appConfig = appConfig;
            _configApiService = configApiService;
        }

        public async Task ApplyThemeAsync(SidebarTheme theme)
        {
            try
            {
                _currentTheme = theme;
                string cssGradient;

                if (theme == SidebarTheme.Custom)
                {
                    // Get custom colors from configuration
                    var color1 = await _configApiService.GetConfigValueAsync<string>((int)ConfigGroupId.UI, "CustomSidebarColor1", "#6c5ce7");
                    var color2 = await _configApiService.GetConfigValueAsync<string>((int)ConfigGroupId.UI, "CustomSidebarColor2", "#a29bfe");
                    cssGradient = $"linear-gradient(180deg, {color1} 0%, {color2} 70%)";
                }
                else
                {
                    cssGradient = theme.GetCssGradient();
                }

                // Set CSS variable for immediate application
                await _jsRuntime.InvokeVoidAsync("setSidebarGradient", cssGradient);

                // Also inject CSS for additional elements that need the theme
                var css = $@"
                    .offcanvas-body {{
                        background-image: {cssGradient} !important;
                    }}
                    .offcanvas-header {{
                        background-image: {cssGradient} !important;
                    }}
                ";

                // Inject CSS into the document
                await _jsRuntime.InvokeVoidAsync("applySidebarTheme", css);

                _logger.LogInformation("Applied sidebar theme: {Theme}", theme);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to apply sidebar theme: {Theme}", theme);
            }
        }

        public async Task<SidebarTheme> GetCurrentThemeAsync()
        {
            try
            {
                // First try to get from API (most current)
                var themeValue = await _configApiService.GetConfigValueAsync<int>((int)ConfigGroupId.UI, "SidebarTheme", (int)SidebarTheme.BluePurple);
                if (Enum.IsDefined(typeof(SidebarTheme), themeValue))
                {
                    return (SidebarTheme)themeValue;
                }

                // Fallback to app configuration
                return _appConfig.SidebarTheme;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get current sidebar theme from configuration");
                return SidebarTheme.BluePurple;
            }
        }

        public async Task InitializeAsync()
        {
            try
            {
                var theme = await GetCurrentThemeAsync();
                await ApplyThemeAsync(theme);
                _logger.LogInformation("Sidebar theme service initialized with theme: {Theme}", theme);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize sidebar theme service");
                // Apply default theme as fallback
                try
                {
                    await ApplyThemeAsync(SidebarTheme.BluePurple);
                }
                catch (Exception fallbackEx)
                {
                    _logger.LogError(fallbackEx, "Failed to apply fallback theme");
                }
            }
        }
    }
}
