<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.15" />
	<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.15" />
	<PackageReference Include="OpenIddict.Server.AspNetCore" Version="7.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />

    <PackageReference Include="OpenIddict.AspNetCore" Version="7.0.0" />
    <PackageReference Include="OpenIddict.EntityFrameworkCore" Version="7.0.0" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
	<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.15" />
	<PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ShiningCMusicApp\ShiningCMusicApp.csproj" />
    <ProjectReference Include="..\ShiningCMusicBFF\ShiningCMusicBFF.csproj" />
    <ProjectReference Include="..\ShiningCMusicCommon\ShiningCMusicCommon.csproj" />
  </ItemGroup>

  <!-- Custom target to include BFF project when publishing -->
  <Target Name="PublishBFF" AfterTargets="Publish">
    <PropertyGroup>
      <BFFPublishPath>$(PublishDir)bff</BFFPublishPath>
    </PropertyGroup>

    <!-- Create BFF directory -->
    <MakeDir Directories="$(BFFPublishPath)" />

    <!-- Publish BFF project to subdirectory -->
    <MSBuild Projects="..\ShiningCMusicBFF\ShiningCMusicBFF.csproj" Targets="Publish" Properties="PublishDir=$(BFFPublishPath)\;Configuration=$(Configuration);Platform=$(Platform)" />

    <Message Text="BFF project published to: $(BFFPublishPath)" Importance="high" />
  </Target>

</Project>
