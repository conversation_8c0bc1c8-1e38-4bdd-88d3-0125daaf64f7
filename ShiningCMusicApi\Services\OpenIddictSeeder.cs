using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using OpenIddict.Abstractions;
using ShiningCMusicApi.Infrastructure;
using ShiningCMusicApi.Services.Interfaces;
using System.Text.Json;
using static OpenIddict.Abstractions.OpenIddictConstants;

namespace ShiningCMusicApi.Services;

public class OpenIddictSeeder(IServiceProvider serviceProvider, ILogger<OpenIddictSeeder> logger) : IHostedService
{
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Starting OpenIddict seeder...");

            await using var scope = serviceProvider.CreateAsyncScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            await context.Database.EnsureCreatedAsync(cancellationToken);

            var manager = scope.ServiceProvider.GetRequiredService<IOpenIddictApplicationManager>();
            var clientSecretService = scope.ServiceProvider.GetRequiredService<IClientSecretService>();

            // Check if there are any existing clients in the database
            var hasExistingClients = false;
            await foreach (var client in manager.ListAsync(cancellationToken: cancellationToken))
            {
                hasExistingClients = true;
                break;
            }

            if (!hasExistingClients)
            {
                logger.LogWarning("No existing clients found. Creating default client...");

                await manager.CreateAsync(new OpenIddictApplicationDescriptor
                {
                    ClientId = "wasm_client",
                    ClientSecret = "AE6qbzhQ08kW",
                    ConsentType = ConsentTypes.Implicit, // Changed from Explicit
                    DisplayName = "Blazor WebAssembly Client",
                    ClientType = ClientTypes.Confidential,
                    Properties =
                        {
                            ["access_token_lifetime"] = JsonSerializer.SerializeToElement("7200")
                        },
                    Permissions =
                        {
                            Permissions.Endpoints.Token,
                            Permissions.GrantTypes.ClientCredentials,
                            Permissions.Prefixes.Scope + "ShiningCMusicApi"
                        }
                }, cancellationToken);

                // Also create in ClientSecrets table
                try
                {
                    await clientSecretService.CreateClientSecretAsync("wasm_client", "AE6qbzhQ08kW", "Blazor WebAssembly Client", 7200);
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "ClientSecret for wasm_client may already exist");
                }

                logger.LogInformation("Created wasm_client in OpenIddict");
                return;
            }

            logger.LogInformation("Existing clients found in database. Skipping default client creation.");
            logger.LogInformation("OpenIddict seeder completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to seed OpenIddict data");
            throw;
        }
    }

    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}
