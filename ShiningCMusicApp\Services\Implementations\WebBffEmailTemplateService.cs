using Microsoft.AspNetCore.Components.WebAssembly.Http;
using Microsoft.Extensions.Logging;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class WebBffEmailTemplateService : IWebBffEmailTemplateService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<WebBffEmailTemplateService> _logger;
        private readonly string _baseUrl;

        public WebBffEmailTemplateService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<WebBffEmailTemplateService> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<List<EmailTemplate>> GetTemplatesAsync()
        {
            try
            {
                _logger.LogInformation("Fetching email templates from Web BFF");

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/email-templates");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var templates = JsonSerializer.Deserialize<List<EmailTemplate>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Email templates loaded successfully. Count: {Count}", templates?.Count ?? 0);
                    return templates ?? new List<EmailTemplate>();
                }

                _logger.LogWarning("Failed to fetch email templates. Status: {StatusCode}", response.StatusCode);
                return new List<EmailTemplate>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching email templates from Web BFF");
                return new List<EmailTemplate>();
            }
        }

        public async Task<EmailTemplate?> GetTemplateAsync(string templateName)
        {
            try
            {
                _logger.LogInformation("Fetching email template {TemplateName} from Web BFF", templateName);

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/email-templates/{templateName}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var template = JsonSerializer.Deserialize<EmailTemplate>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Email template {TemplateName} loaded successfully", templateName);
                    return template;
                }

                _logger.LogWarning("Failed to fetch email template {TemplateName}. Status: {StatusCode}", templateName, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching email template {TemplateName} from Web BFF", templateName);
                return null;
            }
        }

        public async Task<bool> CreateTemplateAsync(EmailTemplate template)
        {
            try
            {
                _logger.LogInformation("Creating email template via Web BFF");

                var json = JsonSerializer.Serialize(template);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/email-templates")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Email template created successfully");
                    return true;
                }

                _logger.LogWarning("Failed to create email template. Status: {StatusCode}", response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating email template via Web BFF");
                return false;
            }
        }

        public async Task<bool> UpdateTemplateAsync(EmailTemplate template)
        {
            try
            {
                _logger.LogInformation("Updating email template {TemplateName} via Web BFF", template.Name);

                var json = JsonSerializer.Serialize(template);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/email-templates/{template.Name}")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Email template {TemplateName} updated successfully", template.Name);
                    return true;
                }

                _logger.LogWarning("Failed to update email template {TemplateName}. Status: {StatusCode}", template.Name, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email template {TemplateName} via Web BFF", template.Name);
                return false;
            }
        }

        public async Task<bool> DeleteTemplateAsync(string templateName)
        {
            try
            {
                _logger.LogInformation("Deleting email template {TemplateName} via Web BFF", templateName);

                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/email-templates/{templateName}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Email template {TemplateName} deleted successfully", templateName);
                    return true;
                }

                _logger.LogWarning("Failed to delete email template {TemplateName}. Status: {StatusCode}", templateName, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting email template {TemplateName} via Web BFF", templateName);
                return false;
            }
        }

        public async Task<string> PreviewTemplateAsync(string templateName, Dictionary<string, string> sampleData)
        {
            try
            {
                _logger.LogInformation("Previewing email template {TemplateName} via Web BFF", templateName);

                var requestData = new { TemplateName = templateName, SampleData = sampleData };
                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/email-templates/preview")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<JsonElement>(responseContent);

                    if (result.TryGetProperty("preview", out var previewElement))
                    {
                        var preview = previewElement.GetString() ?? string.Empty;
                        _logger.LogInformation("Email template {TemplateName} preview generated successfully", templateName);
                        return preview;
                    }
                }

                _logger.LogWarning("Failed to preview email template {TemplateName}. Status: {StatusCode}", templateName, response.StatusCode);
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error previewing email template {TemplateName} via Web BFF", templateName);
                return string.Empty;
            }
        }

        public async Task<List<EmailAttachment>> GetTemplateAttachmentsAsync(string templateName)
        {
            try
            {
                _logger.LogInformation("Loading attachments for email template {TemplateName} via Web BFF", templateName);

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/email-templates/{templateName}/attachments");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var attachments = JsonSerializer.Deserialize<List<EmailAttachment>>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    }) ?? new List<EmailAttachment>();

                    _logger.LogInformation("Loaded {Count} attachments for email template {TemplateName}", attachments.Count, templateName);
                    return attachments;
                }

                _logger.LogWarning("Failed to load attachments for email template {TemplateName}. Status: {StatusCode}", templateName, response.StatusCode);
                return new List<EmailAttachment>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading attachments for email template {TemplateName} via Web BFF", templateName);
                return new List<EmailAttachment>();
            }
        }

        public async Task<EmailAttachment?> AddAttachmentAsync(EmailAttachment attachment)
        {
            try
            {
                _logger.LogInformation("Adding attachment {AttachmentName} to email template {TemplateName} via Web BFF", attachment.AttachmentName, attachment.TemplateName);

                var json = JsonSerializer.Serialize(attachment);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/email-templates/attachments")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<JsonElement>(responseContent);

                    if (result.TryGetProperty("attachment", out var attachmentElement))
                    {
                        var createdAttachment = JsonSerializer.Deserialize<EmailAttachment>(attachmentElement.GetRawText(), new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        _logger.LogInformation("Attachment {AttachmentName} added successfully to template {TemplateName}", attachment.AttachmentName, attachment.TemplateName);
                        return createdAttachment;
                    }
                }

                _logger.LogWarning("Failed to add attachment {AttachmentName} to template {TemplateName}. Status: {StatusCode}", attachment.AttachmentName, attachment.TemplateName, response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding attachment {AttachmentName} to template {TemplateName} via Web BFF", attachment.AttachmentName, attachment.TemplateName);
                return null;
            }
        }

        public async Task<bool> DeleteAttachmentAsync(int attachmentId)
        {
            try
            {
                _logger.LogInformation("Deleting attachment {AttachmentId} via Web BFF", attachmentId);

                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/email-templates/attachments/{attachmentId}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Attachment {AttachmentId} deleted successfully", attachmentId);
                    return true;
                }

                _logger.LogWarning("Failed to delete attachment {AttachmentId}. Status: {StatusCode}", attachmentId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting attachment {AttachmentId} via Web BFF", attachmentId);
                return false;
            }
        }
    }
}
