using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicCommon.Enums;
using ShiningCMusicCommon.Models;
using ShiningCMusicApp.Services.Interfaces;

namespace ShiningCMusicApp.Components
{
    public partial class ThemeLoader : ComponentBase
    {
        [Parameter] public RenderFragment? ChildContent { get; set; }
        [Parameter] public string LoadingMessage { get; set; } = "Loading your application...";
        
        [Inject] private IConfigApiService ConfigApiService { get; set; } = default!;
        [Inject] private IJSRuntime JSRuntime { get; set; } = default!;
        [Inject] private ILogger<ThemeLoader> Logger { get; set; } = default!;
        
        private bool isThemeLoaded = false;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadAndApplyThemeAsync();
                isThemeLoaded = true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed to load theme, proceeding with default");
                // Apply default theme and continue
                await ApplyDefaultThemeAsync();
                isThemeLoaded = true;
            }
            
            StateHasChanged();
        }

        private async Task LoadAndApplyThemeAsync()
        {
            // Get user's theme from configuration
            var themeValue = await ConfigApiService.GetConfigValueAsync<int>((int)ConfigGroupId.UI, "SidebarTheme", (int)SidebarTheme.BluePurple);
            
            SidebarTheme theme = SidebarTheme.BluePurple;
            if (Enum.IsDefined(typeof(SidebarTheme), themeValue))
            {
                theme = (SidebarTheme)themeValue;
            }

            string cssGradient;
            
            if (theme == SidebarTheme.Custom)
            {
                // Get custom colors from configuration
                var color1 = await ConfigApiService.GetConfigValueAsync<string>((int)ConfigGroupId.UI, "CustomSidebarColor1", "#6c5ce7");
                var color2 = await ConfigApiService.GetConfigValueAsync<string>((int)ConfigGroupId.UI, "CustomSidebarColor2", "#a29bfe");
                cssGradient = $"linear-gradient(180deg, {color1} 0%, {color2} 70%)";
            }
            else
            {
                cssGradient = theme.GetCssGradient();
            }

            // Apply theme immediately
            await JSRuntime.InvokeVoidAsync("setSidebarGradient", cssGradient);
            
            Logger.LogInformation("Theme loaded and applied: {Theme}", theme);
        }

        private async Task ApplyDefaultThemeAsync()
        {
            try
            {
                var defaultGradient = SidebarTheme.BluePurple.GetCssGradient();
                await JSRuntime.InvokeVoidAsync("setSidebarGradient", defaultGradient);
                Logger.LogInformation("Default theme applied");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed to apply default theme");
            }
        }
    }
}
