using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services
{
    public class StudentApiService : IStudentApiService
    {
        private readonly HttpClient _httpClient;
        private readonly IAuthenticationService _authService;
        private readonly string _baseUrl;

        public StudentApiService(HttpClient httpClient, IAuthenticationService authService, ApiConfiguration apiConfig)
        {
            _httpClient = httpClient;
            _authService = authService;
            _baseUrl = apiConfig.BaseUrl;
        }

        private async Task<bool> SetAuthorizationHeaderAsync()
        {
            var token = await _authService.GetAccessTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                return true;
            }
            return false;
        }

        public async Task<List<Student>> GetStudentsAsync()
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return new List<Student>();
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/students");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var students = JsonSerializer.Deserialize<List<Student>>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return students ?? new List<Student>();
                }

                return new List<Student>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting students: {ex.Message}");
                return new List<Student>();
            }
        }

        public async Task<Student?> GetStudentAsync(int id)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/students/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Student>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting student: {ex.Message}");
                return null;
            }
        }

        public async Task<Student?> CreateStudentAsync(Student student)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var json = JsonSerializer.Serialize(student);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_baseUrl}/students", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return JsonSerializer.Deserialize<Student>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating student: {ex.Message}");
                return null;
            }
        }

        public async Task<bool> UpdateStudentAsync(int id, Student student)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var json = JsonSerializer.Serialize(student);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PutAsync($"{_baseUrl}/students/{id}", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating student: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteStudentAsync(int id)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var response = await _httpClient.DeleteAsync($"{_baseUrl}/students/{id}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting student: {ex.Message}");
                return false;
            }
        }
    }
}
