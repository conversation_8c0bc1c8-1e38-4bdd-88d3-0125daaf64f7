using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IWebBffSettingsService
    {
        Task<SettingsData?> GetSettingsDataAsync();
        Task<Config?> GetConfigAsync(int configId);
        Task<bool> UpdateConfigAsync(int configId, string value);
        Task<BatchUpdateResult> BatchUpdateConfigsAsync(List<ConfigUpdate> updates);
        Task<Dictionary<string, bool>> GetBackgroundProcessorStatesAsync();
        Task<bool> ToggleBackgroundProcessorAsync(string processorName, bool enabled);

        // Configuration value methods for theme loading
        Task<string?> GetConfigValueAsync(int groupId, string key);
        Task<T?> GetConfigValueAsync<T>(int groupId, string key, T? defaultValue = default);
    }

    public class SettingsData
    {
        public List<ConfigGroup> ConfigGroups { get; set; } = new();
    }

    public class ConfigUpdate
    {
        public int ConfigId { get; set; }
        public string Value { get; set; } = string.Empty;
    }

    public class BatchUpdateResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<ConfigUpdateResult> Results { get; set; } = new();
    }

    public class ConfigUpdateResult
    {
        public int ConfigId { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
