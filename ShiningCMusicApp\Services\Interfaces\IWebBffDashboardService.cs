using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IWebBffDashboardService
    {
        Task<DashboardData?> GetDashboardDataAsync();
        Task<DashboardStats?> GetQuickStatsAsync();

        // Individual data access methods for backward compatibility
        Task<List<ScheduleEvent>> GetLessonsAsync();
        Task<List<Student>> GetStudentsAsync();
        Task<List<Tutor>> GetTutorsAsync();
        Task<List<Subject>> GetSubjectsAsync();
        Task<List<Location>> GetLocationsAsync();

        // Lesson Management
        Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson);
        Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson);
        Task<bool> DeleteLessonAsync(int id);
    }

    public class DashboardData
    {
        public List<ScheduleEvent> Lessons { get; set; } = new();
        public List<Student> Students { get; set; } = new();
        public List<Tutor> Tutors { get; set; } = new();
        public List<Subject> Subjects { get; set; } = new();
        public List<Location> Locations { get; set; } = new();
        public DashboardStats Stats { get; set; } = new();
        public string UserRole { get; set; } = string.Empty;
        public DateTime LoadedAt { get; set; }
    }

    public class DashboardStats
    {
        public int TotalLessons { get; set; }
        public int TotalStudents { get; set; }
        public int TotalTutors { get; set; }
        public int TotalSubjects { get; set; }
        public int UpcomingLessons { get; set; }
        public int TodayLessons { get; set; }
        public int ThisWeekLessons { get; set; }
    }
}
