using System.Text.Json;
using System.Text;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using Microsoft.AspNetCore.Components.WebAssembly.Http;

namespace ShiningCMusicApp.Services.Implementations
{
    public class WebBffDashboardService : IWebBffDashboardService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly ILogger<WebBffDashboardService> _logger;

        public WebBffDashboardService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<WebBffDashboardService> logger)
        {
            _httpClient = httpClient;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
            _logger = logger;
        }

        public async Task<DashboardData?> GetDashboardDataAsync()
        {
            try
            {
                _logger.LogInformation("Fetching dashboard data from Web BFF");

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/dashboard");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var dashboardData = JsonSerializer.Deserialize<DashboardData>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Dashboard data loaded successfully. Lessons: {LessonCount}, Students: {StudentCount}", 
                        dashboardData?.Lessons?.Count ?? 0, dashboardData?.Students?.Count ?? 0);

                    return dashboardData;
                }
                else
                {
                    _logger.LogWarning("Failed to load dashboard data. Status: {StatusCode}", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard data from Web BFF");
                return null;
            }
        }

        public async Task<DashboardStats?> GetQuickStatsAsync()
        {
            try
            {
                _logger.LogInformation("Fetching quick stats from Web BFF");

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/dashboard/quick-stats");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var stats = JsonSerializer.Deserialize<DashboardStats>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Quick stats loaded successfully. Total lessons: {TotalLessons}, Today: {TodayLessons}",
                        stats?.TotalLessons ?? 0, stats?.TodayLessons ?? 0);

                    return stats;
                }
                else
                {
                    _logger.LogWarning("Failed to load quick stats. Status: {StatusCode}", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading quick stats from Web BFF");
                return null;
            }
        }

        // Individual data access methods for backward compatibility
        public async Task<List<ScheduleEvent>> GetLessonsAsync()
        {
            var dashboardData = await GetDashboardDataAsync();
            return dashboardData?.Lessons ?? new List<ScheduleEvent>();
        }

        public async Task<List<Student>> GetStudentsAsync()
        {
            var dashboardData = await GetDashboardDataAsync();
            return dashboardData?.Students ?? new List<Student>();
        }

        public async Task<List<Tutor>> GetTutorsAsync()
        {
            var dashboardData = await GetDashboardDataAsync();
            return dashboardData?.Tutors ?? new List<Tutor>();
        }

        public async Task<List<Subject>> GetSubjectsAsync()
        {
            var dashboardData = await GetDashboardDataAsync();
            return dashboardData?.Subjects ?? new List<Subject>();
        }

        public async Task<List<Location>> GetLocationsAsync()
        {
            var dashboardData = await GetDashboardDataAsync();
            return dashboardData?.Locations ?? new List<Location>();
        }

        // Lesson Management
        public async Task<ScheduleEvent?> CreateLessonAsync(ScheduleEvent lesson)
        {
            try
            {
                _logger.LogInformation("Creating lesson via Web BFF");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/dashboard/lessons");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(lesson);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdLesson = JsonSerializer.Deserialize<ScheduleEvent>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Lesson created successfully via Web BFF");
                    return createdLesson;
                }
                else
                {
                    _logger.LogWarning("Failed to create lesson via Web BFF. Status: {StatusCode}", response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating lesson via Web BFF");
                return null;
            }
        }

        public async Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson)
        {
            try
            {
                _logger.LogInformation("Updating lesson {LessonId} via Web BFF", id);

                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/dashboard/lessons/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var json = JsonSerializer.Serialize(lesson);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Lesson {LessonId} updated successfully via Web BFF", id);
                    return true;
                }
                else
                {
                    _logger.LogWarning("Failed to update lesson {LessonId} via Web BFF. Status: {StatusCode}", id, response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating lesson {LessonId} via Web BFF", id);
                return false;
            }
        }

        public async Task<bool> DeleteLessonAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting lesson {LessonId} via Web BFF", id);

                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/dashboard/lessons/{id}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Lesson {LessonId} deleted successfully via Web BFF", id);
                    return true;
                }
                else
                {
                    _logger.LogWarning("Failed to delete lesson {LessonId} via Web BFF. Status: {StatusCode}", id, response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting lesson {LessonId} via Web BFF", id);
                return false;
            }
        }
    }
}
