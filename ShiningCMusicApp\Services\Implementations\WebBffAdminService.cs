using Microsoft.AspNetCore.Components.WebAssembly.Http;
using Microsoft.Extensions.Logging;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class WebBffAdminService : IWebBffAdminService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<WebBffAdminService> _logger;
        private readonly string _baseUrl;

        public WebBffAdminService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<WebBffAdminService> logger, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _logger = logger;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
        }

        public async Task<AdminData?> GetAdminDataAsync()
        {
            try
            {
                _logger.LogInformation("Fetching admin data from Web BFF");

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/admin/data");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var data = JsonSerializer.Deserialize<AdminData>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Admin data loaded successfully. Users: {UserCount}, Subjects: {SubjectCount}, Locations: {LocationCount}",
                        data?.Users?.Count ?? 0, data?.Subjects?.Count ?? 0, data?.Locations?.Count ?? 0);
                    return data;
                }

                _logger.LogWarning("Failed to fetch admin data. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching admin data from Web BFF");
                return null;
            }
        }

        // User Management
        public async Task<bool> CreateUserAsync(User user)
        {
            return await PostAsync("users", user, "user");
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            return await PutAsync($"users/{user.LoginName}", user, "user", user.LoginName);
        }

        public async Task<bool> DeleteUserAsync(string loginName)
        {
            return await DeleteAsync($"users/{loginName}", "user", loginName);
        }

        // Subject Management
        public async Task<bool> CreateSubjectAsync(Subject subject)
        {
            return await PostAsync("subjects", subject, "subject");
        }

        public async Task<bool> UpdateSubjectAsync(Subject subject)
        {
            return await PutAsync($"subjects/{subject.SubjectId}", subject, "subject", subject.SubjectId.ToString());
        }

        public async Task<bool> DeleteSubjectAsync(int subjectId)
        {
            return await DeleteAsync($"subjects/{subjectId}", "subject", subjectId.ToString());
        }

        // Location Management
        public async Task<bool> CreateLocationAsync(Location location)
        {
            return await PostAsync("locations", location, "location");
        }

        public async Task<bool> UpdateLocationAsync(Location location)
        {
            return await PutAsync($"locations/{location.LocationId}", location, "location", location.LocationId.ToString());
        }

        public async Task<bool> DeleteLocationAsync(int locationId)
        {
            return await DeleteAsync($"locations/{locationId}", "location", locationId.ToString());
        }

        // Tutor Management
        public async Task<bool> CreateTutorAsync(Tutor tutor)
        {
            return await PostAsync("tutors", tutor, "tutor");
        }

        public async Task<bool> UpdateTutorAsync(Tutor tutor)
        {
            return await PutAsync($"tutors/{tutor.TutorId}", tutor, "tutor", tutor.TutorId.ToString());
        }

        public async Task<bool> DeleteTutorAsync(int tutorId)
        {
            return await DeleteAsync($"tutors/{tutorId}", "tutor", tutorId.ToString());
        }

        public async Task<bool> UpdateTutorColorAsync(int tutorId, string color)
        {
            try
            {
                _logger.LogInformation("Updating tutor {TutorId} color to {Color} via Web BFF", tutorId, color);

                var request = new HttpRequestMessage(HttpMethod.Patch, $"{_baseUrl}/admin/tutors/{tutorId}/color");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var colorRequest = new { Color = color };
                var json = JsonSerializer.Serialize(colorRequest);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Tutor {TutorId} color updated successfully via Web BFF", tutorId);
                    return true;
                }
                else
                {
                    _logger.LogWarning("Failed to update tutor {TutorId} color via Web BFF. Status: {StatusCode}", tutorId, response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating tutor {TutorId} color via Web BFF", tutorId);
                return false;
            }
        }

        // Student Management
        public async Task<bool> CreateStudentAsync(Student student)
        {
            return await PostAsync("students", student, "student");
        }

        public async Task<bool> UpdateStudentAsync(Student student)
        {
            return await PutAsync($"students/{student.StudentId}", student, "student", student.StudentId.ToString());
        }

        public async Task<bool> DeleteStudentAsync(int studentId)
        {
            return await DeleteAsync($"students/{studentId}", "student", studentId.ToString());
        }

        // Helper methods
        private async Task<bool> PostAsync<T>(string endpoint, T data, string entityType)
        {
            try
            {
                _logger.LogInformation("Creating {EntityType} via Web BFF", entityType);

                var json = JsonSerializer.Serialize(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/admin/{endpoint}")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("{EntityType} created successfully", entityType);
                    return true;
                }

                _logger.LogWarning("Failed to create {EntityType}. Status: {StatusCode}", entityType, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating {EntityType} via Web BFF", entityType);
                return false;
            }
        }

        private async Task<bool> PutAsync<T>(string endpoint, T data, string entityType, string entityId)
        {
            try
            {
                _logger.LogInformation("Updating {EntityType} {EntityId} via Web BFF", entityType, entityId);

                var json = JsonSerializer.Serialize(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Put, $"{_baseUrl}/admin/{endpoint}")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("{EntityType} {EntityId} updated successfully", entityType, entityId);
                    return true;
                }

                _logger.LogWarning("Failed to update {EntityType} {EntityId}. Status: {StatusCode}", entityType, entityId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating {EntityType} {EntityId} via Web BFF", entityType, entityId);
                return false;
            }
        }

        private async Task<bool> DeleteAsync(string endpoint, string entityType, string entityId)
        {
            try
            {
                _logger.LogInformation("Deleting {EntityType} {EntityId} via Web BFF", entityType, entityId);

                var request = new HttpRequestMessage(HttpMethod.Delete, $"{_baseUrl}/admin/{endpoint}");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("{EntityType} {EntityId} deleted successfully", entityType, entityId);
                    return true;
                }

                _logger.LogWarning("Failed to delete {EntityType} {EntityId}. Status: {StatusCode}", entityType, entityId, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting {EntityType} {EntityId} via Web BFF", entityType, entityId);
                return false;
            }
        }

        // User Role Management
        public async Task<List<UserRole>?> GetUserRolesAsync()
        {
            try
            {
                _logger.LogInformation("Fetching user roles from Web BFF");

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/admin/user-roles");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var result = JsonSerializer.Deserialize<UserRolesResponse>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (result?.Success == true && result.UserRoles != null)
                    {
                        _logger.LogInformation("User roles fetched successfully from Web BFF");
                        return result.UserRoles;
                    }
                }

                _logger.LogWarning("Failed to fetch user roles from Web BFF. Status: {StatusCode}", response.StatusCode);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching user roles from Web BFF");
                return null;
            }
        }

        public async Task<bool> CreateUserRoleAsync(UserRole userRole)
        {
            return await PostAsync("user-roles", userRole, "UserRole");
        }

        public async Task<bool> UpdateUserRoleAsync(UserRole userRole)
        {
            return await PutAsync($"user-roles/{userRole.ID}", userRole, "UserRole", userRole.ID.ToString());
        }

        public async Task<bool> DeleteUserRoleAsync(int roleId)
        {
            return await DeleteAsync($"user-roles/{roleId}", "UserRole", roleId.ToString());
        }
    }

    public class UserRolesResponse
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public List<UserRole>? UserRoles { get; set; }
    }
}
