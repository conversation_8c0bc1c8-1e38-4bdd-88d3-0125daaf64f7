using Microsoft.AspNetCore.Mvc;
using ShiningCMusicCommon.Models;
using ShiningCMusicCommon.Enums;
using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ConfigurationController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IConfigService _configService;
        private readonly ILogger<ConfigurationController> _logger;

        public ConfigurationController(IConfiguration configuration, IConfigService configService, ILogger<ConfigurationController> logger)
        {
            _configuration = configuration;
            _configService = configService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetConfiguration()
        {
            try
            {
                // Get API base URL from environment variable or configuration (fallback)
                var apiBaseUrl = Environment.GetEnvironmentVariable("API_BASE_URL")
                    ?? _configuration["ApiBaseUrl"]
                    ?? "https://localhost:7268/";

                // Ensure it ends with /
                if (!apiBaseUrl.EndsWith('/'))
                {
                    apiBaseUrl += "/";
                }

                var syncfusionLicense = Environment.GetEnvironmentVariable("SYNCFUSION_LICENSE")
                    ?? _configuration["SyncfusionLicense"]
                    ?? "MzkxNzI4MkAzMjM5MmUzMDJlMzAzYjMyMzkzYmlCSHVjUFNHMVcwWDI0Mm5reFc2M21MbXA4cEFVcWRRWXl1eFFUWnlXYTA9";

                // Try to get configuration values from database first, then fallback to environment/config
                var sessionTimeoutMinutes = await _configService.GetConfigValueAsync<int>((int)ConfigGroupId.General, "SessionTimeoutMinutes", 30);
                var showActionButtonLabel = await _configService.GetConfigValueAsync<bool>((int)ConfigGroupId.UI, "ShowActionButtonLabel", false);
                var sidebarThemeValue = await _configService.GetConfigValueAsync<int>((int)ConfigGroupId.UI, "SidebarTheme", 1);
                var sidebarTheme = Enum.IsDefined(typeof(SidebarTheme), sidebarThemeValue) ? (SidebarTheme)sidebarThemeValue : SidebarTheme.BluePurple;
                var customSidebarColor1 = await _configService.GetConfigValueAsync<string>((int)ConfigGroupId.UI, "CustomSidebarColor1", "#6c5ce7");
                var customSidebarColor2 = await _configService.GetConfigValueAsync<string>((int)ConfigGroupId.UI, "CustomSidebarColor2", "#a29bfe");

                var config = new AppConfiguration
                {
                    ApiBaseUrl = $"{apiBaseUrl}api",
                    SyncfusionLicense = syncfusionLicense,
                    SessionTimeoutMinutes = sessionTimeoutMinutes,
                    ShowActionButtonLabel = showActionButtonLabel,
                    SidebarTheme = sidebarTheme,
                    CustomSidebarColor1 = customSidebarColor1 ?? "#6c5ce7",
                    CustomSidebarColor2 = customSidebarColor2 ?? "#a29bfe"
                };

                _logger.LogInformation("Configuration loaded from database - SessionTimeout: {SessionTimeout}, ShowActionButtonLabel: {ShowActionButtonLabel}",
                    sessionTimeoutMinutes, showActionButtonLabel);

                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading configuration from database, falling back to environment/config values");

                // Fallback to original logic if database fails
                var apiBaseUrl = Environment.GetEnvironmentVariable("API_BASE_URL")
                    ?? _configuration["ApiBaseUrl"]
                    ?? "https://localhost:7268/";

                if (!apiBaseUrl.EndsWith('/'))
                {
                    apiBaseUrl += "/";
                }

                var sessionTimeoutMinutes = 30;
                if (int.TryParse(Environment.GetEnvironmentVariable("SESSION_TIMEOUT_MINUTES"), out var envTimeout))
                {
                    sessionTimeoutMinutes = envTimeout;
                }

                var showActionButtonLabel = false;
                if (bool.TryParse(Environment.GetEnvironmentVariable("SHOW_ACTION_BUTTON_LABEL"), out var envShowLabel))
                {
                    showActionButtonLabel = envShowLabel;
                }

                var config = new AppConfiguration
                {
                    ApiBaseUrl = $"{apiBaseUrl}api",
                    SyncfusionLicense = _configuration["SyncfusionLicense"]
                        ?? Environment.GetEnvironmentVariable("SYNCFUSION_LICENSE")
                        ?? "MzkxNzI4MkAzMjM5MmUzMDJlMzAzYjMyMzkzYmlCSHVjUFNHMVcwWDI0Mm5reFc2M21MbXA4cEFVcWRRWXl1eFFUWnlXYTA9",
                    SessionTimeoutMinutes = sessionTimeoutMinutes,
                    ShowActionButtonLabel = showActionButtonLabel,
                    SidebarTheme = SidebarTheme.BluePurple, // Default fallback
                    CustomSidebarColor1 = "#6c5ce7", // Default custom colors
                    CustomSidebarColor2 = "#a29bfe"
                };

                return Ok(config);
            }
        }
    }
}
